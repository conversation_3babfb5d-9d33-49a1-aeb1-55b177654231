/**
 * Policy Data Transformer Service
 * 
 * Transforms TPA API responses to internal Policy and Claim types
 * Handles data validation, sanitization, and statistics calculation
 */

import type {
  CustomerPoliciesData,
  Policy,
  Claim,
  PolicyStatistics,
  PolicyProduct,
  PolicyStatus,
  PolicyType,
  ClaimStatus,
  ClaimType
} from '$lib/types/customer';

import type { WorkflowExecutionContext } from './policy-claims-workflow.types';

export class PolicyDataTransformer {
  
  /**
   * Transform workflow execution results to CustomerPoliciesData format
   */
  transformToCustomerPoliciesData(
    context: WorkflowExecutionContext, 
    customerId: number,
    customerName?: string,
    customerEmail?: string
  ): CustomerPoliciesData {
    console.log('Transforming workflow results to CustomerPoliciesData', {
      execution_id: context.execution_id,
      customer_id: customerId
    });

    // Extract raw data from workflow context
    const rawPolicies = context.step_data.policies || [];
    const rawPolicyDetails = context.step_data.policy_details || [];
    const rawClaimsData = context.step_data.claims_data || [];

    // Transform policies and claims
    const policies = this.transformPolicies(rawPolicies, rawPolicyDetails);
    const claims = this.transformClaims(rawClaimsData);

    // Calculate statistics
    const statistics = this.calculateStatistics(policies, claims);

    return {
      customer_id: customerId,
      customer_name: customerName || `Customer ${customerId}`,
      customer_email: customerEmail || `customer${customerId}@example.com`,
      policies,
      claims,
      statistics,
      last_updated: new Date().toISOString()
    };
  }

  /**
   * Transform TPA policy data to internal Policy type
   */
  private transformPolicies(rawPolicies: any[], rawPolicyDetails: any[]): Policy[] {
    const policies: Policy[] = [];

    for (const rawPolicy of rawPolicies) {
      try {
        // Find corresponding policy details
        const policyDetails = rawPolicyDetails.find(
          detail => detail.PolNo === rawPolicy.PolNo || detail.MemberCode === rawPolicy.MemberCode
        );

        const policy = this.mapPolicyFields(rawPolicy, policyDetails);
        policies.push(policy);
      } catch (error) {
        console.warn('Failed to transform policy:', rawPolicy, error);
      }
    }

    return policies;
  }

  /**
   * Transform TPA claims data to internal Claim type
   */
  private transformClaims(rawClaimsData: any[]): Claim[] {
    const claims: Claim[] = [];

    for (const rawClaim of rawClaimsData) {
      try {
        const claim = this.mapClaimFields(rawClaim);
        claims.push(claim);
      } catch (error) {
        console.warn('Failed to transform claim:', rawClaim, error);
      }
    }

    return claims;
  }

  /**
   * Map TPA policy fields to internal Policy type
   */
  private mapPolicyFields(rawPolicy: any, policyDetails?: any): Policy {
    // Create policy product
    const product: PolicyProduct = {
      id: this.generateId(),
      name: rawPolicy.Name || policyDetails?.ProductName || 'Unknown Product',
      product_type: this.determinePolicyType(rawPolicy.Name || policyDetails?.ProductName),
      description: policyDetails?.ProductDescription,
      category: policyDetails?.Category,
      provider: 'TPA',
      coverage_details: policyDetails?.CoverageDetails ? [policyDetails.CoverageDetails] : [],
      exclusions: policyDetails?.Exclusions ? [policyDetails.Exclusions] : []
    };

    // Determine policy status
    const policyStatus = this.determinePolicyStatus(rawPolicy, policyDetails);

    // Parse dates
    const startDate = this.parseDate(rawPolicy.EffFrom);
    const endDate = this.parseDate(rawPolicy.EffTo);
    const issueDate = this.parseDate(policyDetails?.IssueDate) || startDate;

    return {
      id: this.generateId(),
      policy_number: rawPolicy.PolNo || '',
      customer_id: 0, // Will be set by the calling service

      // Product Information
      product,

      // Policy Details
      policy_status: policyStatus,
      issue_date: issueDate,
      start_date: startDate,
      end_date: endDate,
      renewal_date: this.parseDate(policyDetails?.RenewalDate),

      // Financial Information
      premium_amount: this.parseAmount(policyDetails?.PremiumAmount) || 0,
      coverage_amount: this.parseAmount(policyDetails?.CoverageAmount) || 0,
      deductible: this.parseAmount(policyDetails?.Deductible),
      currency: policyDetails?.Currency || 'THB',
      payment_frequency: this.determinePaymentFrequency(policyDetails?.PaymentFrequency),
      next_payment_date: this.parseDate(policyDetails?.NextPaymentDate),
      insurer: policyDetails?.Insurer || 'TPA',
      plan_code: policyDetails?.PlanCode || rawPolicy.MemberCode || '',
      plan_name: policyDetails?.PlanName || rawPolicy.Name || '',

      // Beneficiaries and Coverage
      beneficiaries: this.parseBeneficiaries(policyDetails?.Beneficiaries),
      coverage_details: policyDetails?.CoverageDetails ? { details: policyDetails.CoverageDetails } : {},

      // Documents
      documents: [],

      // Metadata
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      notes: policyDetails?.Notes || '',
      tags: [],
      is_active: policyStatus === 'ACTIVE'
    };
  }

  /**
   * Map TPA claim fields to internal Claim type
   */
  private mapClaimFields(rawClaim: any): Claim {
    const claimStatus = this.determineClaimStatus(rawClaim.Status);
    const claimType = this.determineClaimType(rawClaim.ClaimType || rawClaim.Type);

    return {
      id: this.generateId(),
      claim_number: rawClaim.ClaimNo || rawClaim.ClaimNumber || '',
      policy_id: 0, // Will be linked by policy number
      customer_id: 0, // Will be set by the calling service

      // Claim Details
      claim_status: claimStatus,
      claim_type: claimType,
      incident_date: this.parseDate(rawClaim.IncidentDate) || this.parseDate(rawClaim.DateOfLoss),
      claim_date: this.parseDate(rawClaim.ClaimDate) || this.parseDate(rawClaim.DateReported),
      settlement_date: this.parseDate(rawClaim.SettlementDate),

      // Financial Information
      claimed_amount: this.parseAmount(rawClaim.Amount) || this.parseAmount(rawClaim.ClaimedAmount) || 0,
      approved_amount: this.parseAmount(rawClaim.ApprovedAmount),
      paid_amount: this.parseAmount(rawClaim.PaidAmount),
      outstanding_amount: this.parseAmount(rawClaim.OutstandingAmount),
      currency: rawClaim.Currency || 'THB',

      // Additional Details
      description: rawClaim.Description || rawClaim.ClaimDescription || '',
      incident_location: rawClaim.IncidentLocation || rawClaim.Location,
      hospital_name: rawClaim.HospitalName || rawClaim.Hospital,
      diagnosis: rawClaim.Diagnosis,
      treatment_details: rawClaim.TreatmentDetails,

      // Documents
      documents: [],

      // Processing Information
      adjuster_name: rawClaim.AdjusterName,
      adjuster_contact: rawClaim.AdjusterContact,
      processing_notes: rawClaim.ProcessingNotes || rawClaim.Notes,

      // Metadata
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      tags: [],
      is_active: true
    };
  }

  /**
   * Calculate statistics from policies and claims data
   */
  private calculateStatistics(policies: Policy[], claims: Claim[]): PolicyStatistics {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Policy statistics
    const totalPolicies = policies.length;
    const activePolicies = policies.filter(p => p.policy_status === 'ACTIVE').length;
    const expiredPolicies = policies.filter(p => p.policy_status === 'EXPIRED').length;
    const pendingPolicies = policies.filter(p => p.policy_status === 'PENDING').length;
    const cancelledPolicies = policies.filter(p => p.policy_status === 'CANCELLED').length;
    const waitingPeriodPolicies = policies.filter(p => p.policy_status === 'WAITING_PERIOD').length;
    
    // Nearly expired policies (within 30 days)
    const nearlyExpiredPolicies = policies.filter(p => {
      if (!p.end_date) return false;
      const endDate = new Date(p.end_date);
      const daysUntilExpiry = (endDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000);
      return daysUntilExpiry > 0 && daysUntilExpiry <= 30;
    }).length;

    // Financial statistics
    const totalPremiumAmount = policies.reduce((sum, p) => sum + (p.premium_amount || 0), 0);
    const totalCoverageAmount = policies.reduce((sum, p) => sum + (p.coverage_amount || 0), 0);
    const averagePremium = totalPolicies > 0 ? totalPremiumAmount / totalPolicies : 0;

    // Claims statistics
    const totalClaims = claims.length;
    const activeClaims = claims.filter(c => ['SUBMITTED', 'UNDER_REVIEW', 'PENDING_DOCUMENTS'].includes(c.claim_status)).length;
    const approvedClaims = claims.filter(c => ['APPROVED', 'PAID'].includes(c.claim_status)).length;
    const rejectedClaims = claims.filter(c => c.claim_status === 'REJECTED').length;
    const totalClaimsAmount = claims.reduce((sum, c) => sum + (c.claimed_amount || 0), 0);
    const totalPaidAmount = claims.reduce((sum, c) => sum + (c.paid_amount || 0), 0);

    // Policy type breakdown
    const policyTypeBreakdown: Record<PolicyType, number> = {
      LIFE: 0,
      HEALTH: 0,
      AUTO: 0,
      PROPERTY: 0,
      TRAVEL: 0,
      DISABILITY: 0,
      CRITICAL_ILLNESS: 0
    };

    policies.forEach(policy => {
      policyTypeBreakdown[policy.product.product_type]++;
    });

    // Recent activity (last 30 days)
    const recentPolicies = policies.filter(p => {
      const issueDate = new Date(p.issue_date);
      return issueDate >= thirtyDaysAgo;
    }).length;

    const recentClaims = claims.filter(c => {
      const claimDate = new Date(c.claim_date);
      return claimDate >= thirtyDaysAgo;
    }).length;

    return {
      total_policies: totalPolicies,
      active_policies: activePolicies,
      expired_policies: expiredPolicies,
      pending_policies: pendingPolicies,
      cancelled_policies: cancelledPolicies,
      waiting_period_policies: waitingPeriodPolicies,
      nearly_expired_policies: nearlyExpiredPolicies,
      total_premium_amount: totalPremiumAmount,
      total_coverage_amount: totalCoverageAmount,
      average_premium: averagePremium,
      total_claims: totalClaims,
      active_claims: activeClaims,
      approved_claims: approvedClaims,
      rejected_claims: rejectedClaims,
      total_claims_amount: totalClaimsAmount,
      total_paid_amount: totalPaidAmount,
      policy_type_breakdown: policyTypeBreakdown,
      recent_policies: recentPolicies,
      recent_claims: recentClaims
    };
  }

  // Utility methods for data transformation

  private generateId(): number {
    return Math.floor(Math.random() * 1000000) + Date.now();
  }

  private parseDate(dateString: string | undefined): string {
    if (!dateString) return new Date().toISOString();
    
    try {
      // Handle various date formats from TPA API
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date().toISOString() : date.toISOString();
    } catch {
      return new Date().toISOString();
    }
  }

  private parseAmount(amountString: string | number | undefined): number | undefined {
    if (typeof amountString === 'number') return amountString;
    if (!amountString) return undefined;
    
    const parsed = parseFloat(String(amountString).replace(/[^\d.-]/g, ''));
    return isNaN(parsed) ? undefined : parsed;
  }

  private determinePolicyType(productName: string | undefined): PolicyType {
    if (!productName) return 'HEALTH';
    
    const name = productName.toLowerCase();
    if (name.includes('life')) return 'LIFE';
    if (name.includes('health') || name.includes('medical')) return 'HEALTH';
    if (name.includes('auto') || name.includes('car') || name.includes('vehicle')) return 'AUTO';
    if (name.includes('property') || name.includes('home')) return 'PROPERTY';
    if (name.includes('travel')) return 'TRAVEL';
    if (name.includes('disability')) return 'DISABILITY';
    if (name.includes('critical') || name.includes('illness')) return 'CRITICAL_ILLNESS';
    
    return 'HEALTH'; // Default
  }

  private determinePolicyStatus(rawPolicy: any, policyDetails?: any): PolicyStatus {
    const now = new Date();
    const endDate = new Date(rawPolicy.EffTo);
    
    if (policyDetails?.Status) {
      const status = policyDetails.Status.toLowerCase();
      if (status.includes('active')) return 'ACTIVE';
      if (status.includes('expired')) return 'EXPIRED';
      if (status.includes('cancelled')) return 'CANCELLED';
      if (status.includes('pending')) return 'PENDING';
      if (status.includes('suspended')) return 'SUSPENDED';
    }
    
    // Determine status based on dates
    if (endDate < now) return 'EXPIRED';
    return 'ACTIVE';
  }

  private determinePaymentFrequency(frequency: string | undefined): 'MONTHLY' | 'QUARTERLY' | 'SEMI_ANNUAL' | 'ANNUAL' {
    if (!frequency) return 'ANNUAL';
    
    const freq = frequency.toLowerCase();
    if (freq.includes('month')) return 'MONTHLY';
    if (freq.includes('quarter')) return 'QUARTERLY';
    if (freq.includes('semi') || freq.includes('half')) return 'SEMI_ANNUAL';
    
    return 'ANNUAL';
  }

  private determineClaimStatus(status: string | undefined): ClaimStatus {
    if (!status) return 'SUBMITTED';
    
    const statusLower = status.toLowerCase();
    if (statusLower.includes('submit')) return 'SUBMITTED';
    if (statusLower.includes('review') || statusLower.includes('process')) return 'UNDER_REVIEW';
    if (statusLower.includes('approve')) return 'APPROVED';
    if (statusLower.includes('reject') || statusLower.includes('deny')) return 'REJECTED';
    if (statusLower.includes('paid')) return 'PAID';
    if (statusLower.includes('closed')) return 'CLOSED';
    if (statusLower.includes('pending') || statusLower.includes('document')) return 'PENDING_DOCUMENTS';
    
    return 'SUBMITTED';
  }

  private determineClaimType(type: string | undefined): ClaimType {
    if (!type) return 'MEDICAL';
    
    const typeLower = type.toLowerCase();
    if (typeLower.includes('death')) return 'DEATH';
    if (typeLower.includes('medical') || typeLower.includes('health')) return 'MEDICAL';
    if (typeLower.includes('accident')) return 'ACCIDENT';
    if (typeLower.includes('property') || typeLower.includes('damage')) return 'PROPERTY_DAMAGE';
    if (typeLower.includes('theft')) return 'THEFT';
    if (typeLower.includes('disability')) return 'DISABILITY';
    if (typeLower.includes('critical') || typeLower.includes('illness')) return 'CRITICAL_ILLNESS';
    if (typeLower.includes('hospital')) return 'HOSPITALIZATION';
    
    return 'MEDICAL';
  }

  private parseBeneficiaries(beneficiariesData: any): any[] | undefined {
    if (!beneficiariesData) return undefined;
    
    try {
      if (Array.isArray(beneficiariesData)) return beneficiariesData;
      if (typeof beneficiariesData === 'string') return JSON.parse(beneficiariesData);
      return [beneficiariesData];
    } catch {
      return undefined;
    }
  }
}
